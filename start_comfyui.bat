@echo off
chcp 65001 >nul
REM ============================================================================
REM ComfyUI V100 SXM2 16GB Optimized Startup Script with INT4 Support
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration
set "CONDA_ENV=comfyui"
set "LISTEN_HOST=0.0.0.0"
set "LISTEN_PORT=18188"

echo ============================================================================
echo                ComfyUI V100 SXM2 16GB Startup Script
echo ============================================================================
echo.
echo Please select startup mode:
echo.
echo 1. Auto Mode                 (Recommended: Smart detection and optimization)
echo 2. Balanced Mode             (14GB limit, stable for most models)
echo 3. Conservative Mode         (13GB limit, maximum stability)
echo 4. Standard Mode             (General V100 configuration)
echo.
set /p choice=Select (1-4, Enter for default 1):

REM Default to smart auto mode
if "%choice%"=="" set choice=1

echo.
echo [INFO] Activating conda environment...
call conda activate %CONDA_ENV%
if errorlevel 1 (
    echo [ERROR] Cannot activate conda environment '%CONDA_ENV%'
    echo Please ensure environment exists: conda create -n %CONDA_ENV% python=3.11
    pause
    exit /b 1
)

echo [SUCCESS] Conda environment activated

if "%choice%"=="1" (
    echo.
    echo ============================================================================
    echo                           Smart Auto Mode
    echo ============================================================================
    echo [INFO] Detecting GPU and model types...
    
    python pkgutil_fix.py
    python utils\gpu_detector.py --output-args > auto_config.tmp 2>auto_error.tmp
    if errorlevel 1 (
        echo [ERROR] Auto detection failed, using default configuration
        type auto_error.tmp
        python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% --reserve-vram 2 --fp16-unet --disable-cuda-malloc --async-offload
    ) else (
        set /p AUTO_ARGS=<auto_config.tmp
        echo [SUCCESS] Auto detection completed
        echo [INFO] Optimization args: !AUTO_ARGS!
        python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% !AUTO_ARGS!
    )
    
    del auto_config.tmp 2>nul
    del auto_error.tmp 2>nul
    
) else if "%choice%"=="2" (
    echo.
    echo ============================================================================
    echo                        Balanced Mode
    echo ============================================================================
    echo [INFO] Configuration: 2GB VRAM reserve, 14GB limit, stable for most models
    echo [INFO] Optimized for reliable operation within 14GB limit
    python pkgutil_fix.py
    python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% --reserve-vram 2 --fp16-unet --fp16-vae --fp16-text-enc --disable-cuda-malloc --async-offload --preview-method latent2rgb --force-channels-last
    
) else if "%choice%"=="3" (
    echo.
    echo ============================================================================
    echo                        Conservative Mode
    echo ============================================================================
    echo [INFO] Configuration: 3GB VRAM reserve, 13GB limit, maximum stability
    echo [INFO] Extra conservative for maximum reliability
    python pkgutil_fix.py
    python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% --reserve-vram 3 --fp16-unet --fp16-vae --fp16-text-enc --disable-cuda-malloc --async-offload --preview-method latent2rgb --force-channels-last
    
) else if "%choice%"=="4" (
    echo.
    echo ============================================================================
    echo                          Standard Mode
    echo ============================================================================
    echo [INFO] General V100 configuration, 14GB limit
    python pkgutil_fix.py
    python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% --reserve-vram 2 --fp16-unet --fp16-text-enc --disable-cuda-malloc --async-offload
    
) else (
    echo [ERROR] Invalid selection, using default configuration...
    python pkgutil_fix.py
    python main.py --listen %LISTEN_HOST% --port %LISTEN_PORT% --reserve-vram 2 --fp16-unet --disable-cuda-malloc --async-offload
)

REM Check startup result
if errorlevel 1 (
    echo.
    echo [ERROR] ComfyUI startup failed
    echo [INFO] Please check:
    echo   1. Conda environment is correctly installed
    echo   2. Dependencies are complete
    echo   3. GPU drivers are working
    echo   4. Sufficient VRAM available
    pause
    exit /b 1
) else (
    echo.
    echo [SUCCESS] ComfyUI started successfully
    echo [INFO] Access URL: http://localhost:%LISTEN_PORT%
    echo [INFO] Network access: http://YOUR_IP_ADDRESS:%LISTEN_PORT%
)

echo.
echo ============================================================================
echo                           Startup Complete
echo ============================================================================
pause
